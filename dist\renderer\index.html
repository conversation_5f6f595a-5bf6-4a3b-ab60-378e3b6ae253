<!doctype html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>桌面AI助手</title><script>// 确保global变量在页面加载时就可用
        if (typeof global === 'undefined') {
            window.global = globalThis;
        }
        // 添加require polyfill
        if (typeof require === 'undefined') {
            window.require = function(module) {
                console.warn('require() is not available in browser context:', module);
                return {};
            };
        }

        // 检查是否在Electron环境中
        if (typeof window.electronAPI === 'undefined') {
            console.log('浏览器环境：提供基础mock API用于UI测试');

            // 简单的内存存储
            const mockStorage = new Map();

            // 设置默认配置
            mockStorage.set('app-config', {
                apiKey: 'browser-test-key',
                model: 'qwen-plus',
                temperature: 0.7,
                maxTokens: 2000,
                autoStart: false,
                showOnStartup: false
            });

            window.electronAPI = {
                window: {
                    minimize: () => {
                        console.log('浏览器环境：模拟最小化');
                        return Promise.resolve();
                    },
                    maximize: () => {
                        console.log('浏览器环境：模拟最大化');
                        return Promise.resolve();
                    },
                    close: () => {
                        console.log('浏览器环境：模拟关闭');
                        return Promise.resolve();
                    },
                },
                app: {
                    quit: () => {
                        console.log('浏览器环境：模拟退出');
                        return Promise.resolve();
                    },
                    getVersion: () => Promise.resolve('1.0.0-浏览器测试版'),
                },
                store: {
                    get: (key) => {
                        console.log('浏览器环境：获取存储', key);
                        return Promise.resolve(mockStorage.get(key));
                    },
                    set: (key, value) => {
                        console.log('浏览器环境：设置存储', key);
                        mockStorage.set(key, value);
                        return Promise.resolve();
                    },
                    delete: (key) => {
                        console.log('浏览器环境：删除存储', key);
                        mockStorage.delete(key);
                        return Promise.resolve();
                    },
                },
                ai: {
                    sendMessage: async (message, sessionId) => {
                        console.log('🌐 浏览器环境：发送消息到真实API', message, sessionId);

                        try {
                            // 从localStorage获取API配置
                            const configStr = localStorage.getItem('app-config');
                            const config = configStr ? JSON.parse(configStr) : null;

                            if (!config || !config.apiKey) {
                                console.warn('⚠️ 未找到API配置，使用模拟响应');
                                window.electronAPI.ai._fallbackToMock(message, sessionId);
                                return Promise.resolve();
                            }

                            console.log('🔑 使用API密钥:', config.apiKey.substring(0, 10) + '...');

                            // 获取会话历史
                            const messagesStr = localStorage.getItem('messages');
                            const allMessages = messagesStr ? JSON.parse(messagesStr) : [];
                            const sessionMessages = allMessages.filter(msg => msg.sessionId === sessionId);

                            // 构建消息历史
                            const messages = [
                                { role: 'system', content: 'You are a helpful assistant.' },
                                ...sessionMessages.map(msg => ({
                                    role: msg.role,
                                    content: msg.content
                                })),
                                { role: 'user', content: message }
                            ];

                            console.log('📝 构建的消息历史:', messages.length, '条消息');

                            // 调用真实API
                            await window.electronAPI.ai._callRealAPI(messages, sessionId, config);

                        } catch (error) {
                            console.error('❌ 真实API调用失败:', error);
                            console.log('🔄 切换到模拟响应');
                            window.electronAPI.ai._fallbackToMock(message, sessionId);
                        }

                        return Promise.resolve();
                    },
                    onStreamChunk: (callback) => {
                        console.log('🔗 设置流式响应回调');
                        window.electronAPI.ai._streamCallback = callback;
                    },
                    onStreamEnd: (callback) => {
                        console.log('🔗 设置结束回调');
                        window.electronAPI.ai._endCallback = callback;
                    },
                    onError: (callback) => {
                        console.log('🔗 设置错误回调');
                        window.electronAPI.ai._errorCallback = callback;
                    },
                    removeAllListeners: () => {
                        console.log('🧹 清理所有回调');
                        window.electronAPI.ai._streamCallback = null;
                        window.electronAPI.ai._endCallback = null;
                        window.electronAPI.ai._errorCallback = null;
                    },

                    // 调用真实API的方法
                    _callRealAPI: async (messages, sessionId, config) => {
                        console.log('🚀 调用通义千问API...');

                        const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${config.apiKey}`
                            },
                            body: JSON.stringify({
                                model: config.model || 'qwen-plus',
                                messages: messages,
                                stream: true,
                                temperature: config.temperature || 0.7,
                                max_tokens: config.maxTokens || 2000
                            })
                        });

                        if (!response.ok) {
                            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                        }

                        console.log('✅ API响应成功，开始处理流式数据');

                        const reader = response.body.getReader();
                        const decoder = new TextDecoder();
                        let fullMessage = '';

                        try {
                            while (true) {
                                const { done, value } = await reader.read();
                                if (done) break;

                                const chunk = decoder.decode(value, { stream: true });
                                const lines = chunk.split('\n');

                                for (const line of lines) {
                                    if (line.startsWith('data: ')) {
                                        const data = line.slice(6).trim();
                                        if (data === '[DONE]') {
                                            console.log('🏁 流式响应结束');
                                            if (window.electronAPI.ai._endCallback) {
                                                window.electronAPI.ai._endCallback({
                                                    sessionId,
                                                    fullMessage
                                                });
                                            }
                                            return;
                                        }

                                        try {
                                            const parsed = JSON.parse(data);
                                            const content = parsed.choices?.[0]?.delta?.content;

                                            if (content) {
                                                fullMessage += content;
                                                console.log('📝 收到内容片段:', content);

                                                if (window.electronAPI.ai._streamCallback) {
                                                    window.electronAPI.ai._streamCallback({
                                                        sessionId,
                                                        chunk: content,
                                                        fullMessage
                                                    });
                                                }
                                            }
                                        } catch (parseError) {
                                            console.warn('⚠️ 解析JSON失败:', parseError, 'data:', data);
                                        }
                                    }
                                }
                            }
                        } finally {
                            reader.releaseLock();
                        }
                    },

                    // 模拟响应的fallback方法
                    _fallbackToMock: (message, sessionId) => {
                        console.log('🎭 使用模拟响应');

                        const mockResponses = [
                            '你好！我是通义千问AI助手。很抱歉，当前无法连接到真实的API服务，这是一个模拟响应。',
                            '我理解您的问题。由于网络或配置问题，当前使用模拟模式为您提供服务。',
                            '感谢您的提问！检测到API连接问题，正在使用本地模拟响应。请检查网络连接和API配置。',
                            '您好！当前处于离线模式。请确保API密钥配置正确且网络连接正常。',
                            '这是一个模拟响应。真实的通义千问API暂时不可用，请稍后重试。'
                        ];

                        const response = mockResponses[Math.floor(Math.random() * mockResponses.length)];
                        const words = response.split('');
                        let currentText = '';

                        console.log('🎭 开始模拟流式响应:', response);

                        // 模拟流式响应
                        words.forEach((word, index) => {
                            setTimeout(() => {
                                currentText += word;
                                console.log('📝 发送字符:', word);

                                if (window.electronAPI.ai._streamCallback) {
                                    window.electronAPI.ai._streamCallback({
                                        sessionId,
                                        chunk: word,
                                        fullMessage: currentText
                                    });
                                }

                                // 最后一个字符时触发结束回调
                                if (index === words.length - 1) {
                                    setTimeout(() => {
                                        console.log('✅ 模拟响应完成:', currentText);
                                        if (window.electronAPI.ai._endCallback) {
                                            window.electronAPI.ai._endCallback({
                                                sessionId,
                                                fullMessage: currentText
                                            });
                                        }
                                    }, 100);
                                }
                            }, index * 50);
                        });
                    }
                }
            };
        } else {
            console.log('Electron环境：使用真实API');
        }</script><style>body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #f5f5f5;
        }
        #root {
            height: 100vh;
            width: 100vw;
        }</style><script defer="defer" src="renderer.js"></script></head><body><div id="root"></div></body></html>