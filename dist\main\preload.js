(()=>{"use strict";const e=require("electron"),r="ai-stream-chunk",i="ai-stream-end",n="ai-error";e.contextBridge.exposeInMainWorld("electronAPI",{window:{minimize:()=>e.ipcRenderer.invoke("window-minimize"),maximize:()=>e.ipcRenderer.invoke("window-maximize"),close:()=>e.ipcRenderer.invoke("window-close")},app:{quit:()=>e.ipcRenderer.invoke("app-quit"),getVersion:()=>e.ipcRenderer.invoke("app-version")},store:{get:r=>e.ipcRenderer.invoke("store-get",r),set:(r,i)=>e.ipcRenderer.invoke("store-set",r,i),delete:r=>e.ipcRenderer.invoke("store-delete",r)},ai:{sendMessage:(r,i)=>e.ipc<PERSON>enderer.invoke("ai-send-message",r,i),onStreamChunk:i=>{e.ipcRenderer.on(r,(e,r)=>i(r))},onStreamEnd:r=>{e.ipcRenderer.on(i,(e,i)=>r(i))},onError:r=>{e.ipcRenderer.on(n,(e,i)=>r(i))},removeAllListeners:()=>{e.ipcRenderer.removeAllListeners(r),e.ipcRenderer.removeAllListeners(i),e.ipcRenderer.removeAllListeners(n)}}})})();