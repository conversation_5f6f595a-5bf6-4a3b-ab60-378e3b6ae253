import { app, BrowserWindow, ipcMain, dialog, Notification } from 'electron';
import * as path from 'path';
import { WindowManager } from './window';
import { TrayManager, TrayCallbacks } from './tray';
import { AutoLaunchManager } from './autoLaunch';
import { StoreManager } from './store';
import { AIService } from './aiService';
import { ShortcutManager } from './shortcuts';
import { IPC_CHANNELS } from '@shared/constants';

class Application {
  private windowManager: WindowManager;
  private trayManager: TrayManager;
  private autoLaunchManager: AutoLaunchManager;
  private storeManager: StoreManager;
  private aiService: AIService;
  private shortcutManager: ShortcutManager;

  constructor() {
    this.windowManager = new WindowManager();
    this.trayManager = new TrayManager();
    this.autoLaunchManager = new AutoLaunchManager();
    this.storeManager = new StoreManager();
    this.aiService = new AIService();
    this.shortcutManager = new ShortcutManager();

    this.initializeApp();
  }

  private initializeApp(): void {
    // 确保只有一个应用实例运行
    const gotTheLock = app.requestSingleInstanceLock();
    
    if (!gotTheLock) {
      app.quit();
      return;
    }

    // 当尝试运行第二个实例时，聚焦到现有窗口
    app.on('second-instance', () => {
      this.windowManager.showWindow();
    });

    // 应用准备就绪
    app.whenReady().then(() => {
      this.onAppReady();
    });

    // 所有窗口关闭时的处理
    app.on('window-all-closed', () => {
      // 在 macOS 上，应用通常会保持活跃状态
      // 但这是 Windows 应用，所以我们隐藏到托盘
      // 不退出应用
    });

    // 应用激活时的处理
    app.on('activate', () => {
      this.windowManager.showWindow();
    });

    // 应用退出前的处理
    app.on('before-quit', (event) => {
      // 注销快捷键
      this.shortcutManager.unregisterShortcuts();
      // 保存应用状态
      this.storeManager.saveAppState();
    });

    this.setupIpcHandlers();
  }

  private showStartupNotification(): void {
    // 检查系统是否支持通知
    if (!Notification.isSupported()) {
      console.log('系统不支持通知');
      return;
    }

    const notification = new Notification({
      title: '桌面AI助手',
      body: '应用已启动并在后台运行\n\n快捷键：\n• Ctrl+Shift+A - 显示/隐藏窗口\n• Ctrl+Shift+N - 新建会话',
      icon: this.getNotificationIcon(),
      silent: false,
      timeoutType: 'default'
    });

    notification.on('click', () => {
      this.windowManager.showWindow();
    });

    notification.show();
  }

  private getNotificationIcon(): string {
    // 尝试获取通知图标
    const path = require('path');
    const fs = require('fs');

    let iconPath: string;
    if (app.isPackaged) {
      iconPath = path.join(process.resourcesPath, 'assets', 'icons');
    } else {
      iconPath = path.join(__dirname, '../../assets/icons');
    }

    const iconFile = path.join(iconPath, 'icon.png');
    if (fs.existsSync(iconFile)) {
      return iconFile;
    }

    // 如果没有图标文件，返回空字符串
    return '';
  }

  private handleNewSession(): void {
    // 显示窗口并发送新建会话的消息
    this.windowManager.showWindow();
    const mainWindow = this.windowManager.getWindow();
    if (mainWindow) {
      mainWindow.webContents.send(IPC_CHANNELS.SESSION_NEW);
    }
  }

  private handleOpenSettings(): void {
    // 显示窗口并发送打开设置的消息
    this.windowManager.showWindow();
    const mainWindow = this.windowManager.getWindow();
    if (mainWindow) {
      mainWindow.webContents.send(IPC_CHANNELS.SETTINGS_OPEN);
    }
  }

  private handleShowAbout(): void {
    // 显示关于对话框
    const mainWindow = this.windowManager.getWindow();
    dialog.showMessageBox(mainWindow || undefined, {
      type: 'info',
      title: '关于桌面AI助手',
      message: '桌面AI助手',
      detail: `版本: ${app.getVersion()}\n\n一个基于Electron的智能桌面助手应用，集成通义千问API，提供便捷的AI对话功能。\n\n© 2024 桌面AI助手`,
      buttons: ['确定'],
      defaultId: 0,
    });
  }

  private async onAppReady(): Promise<void> {
    // 获取应用配置
    const config = this.storeManager.getAppConfig();

    // 创建主窗口（根据配置决定是否显示）
    await this.windowManager.createMainWindow(config.showOnStartup);

    // 创建系统托盘
    const trayCallbacks: TrayCallbacks = {
      onShowWindow: () => this.windowManager.showWindow(),
      onNewSession: () => this.handleNewSession(),
      onOpenSettings: () => this.handleOpenSettings(),
      onShowAbout: () => this.handleShowAbout(),
    };

    this.trayManager.createTray(() => {
      this.windowManager.toggleWindow();
    }, trayCallbacks);

    // 如果应用启动时隐藏，显示通知提醒用户
    if (!config.showOnStartup) {
      this.showStartupNotification();
    }

    // 设置自动启动
    if (config.autoStart) {
      this.autoLaunchManager.enable();
    }

    // 初始化AI服务
    this.aiService.initialize(config.apiKey, this.windowManager.getWindow() || undefined, this.storeManager);

    // 设置快捷键管理器的窗口引用
    const mainWindow = this.windowManager.getWindow();
    if (mainWindow) {
      this.shortcutManager.setMainWindow(mainWindow);
    }

    // 注册全局快捷键
    this.shortcutManager.registerShortcuts();
  }

  private setupIpcHandlers(): void {
    // 窗口管理
    ipcMain.handle(IPC_CHANNELS.WINDOW_MINIMIZE, () => {
      this.windowManager.minimizeWindow();
    });

    ipcMain.handle(IPC_CHANNELS.WINDOW_MAXIMIZE, () => {
      this.windowManager.maximizeWindow();
    });

    ipcMain.handle(IPC_CHANNELS.WINDOW_CLOSE, () => {
      this.windowManager.hideWindow();
    });

    // 应用管理
    ipcMain.handle(IPC_CHANNELS.APP_QUIT, () => {
      app.quit();
    });

    ipcMain.handle(IPC_CHANNELS.APP_VERSION, () => {
      return app.getVersion();
    });

    // 会话管理
    ipcMain.handle(IPC_CHANNELS.SESSION_NEW, () => {
      // 这个处理器主要用于响应前端的新建会话请求
      // 实际的新建会话逻辑在前端处理
      return true;
    });

    // 设置管理
    ipcMain.handle(IPC_CHANNELS.SETTINGS_OPEN, () => {
      // 这个处理器主要用于响应前端的打开设置请求
      // 实际的设置界面在前端处理
      return true;
    });

    // 数据存储
    ipcMain.handle(IPC_CHANNELS.STORE_GET, (_, key: string) => {
      return this.storeManager.get(key);
    });

    ipcMain.handle(IPC_CHANNELS.STORE_SET, (_, key: string, value: any) => {
      return this.storeManager.set(key, value);
    });

    ipcMain.handle(IPC_CHANNELS.STORE_DELETE, (_, key: string) => {
      return this.storeManager.delete(key);
    });

    // AI API
    ipcMain.handle(IPC_CHANNELS.AI_SEND_MESSAGE, async (_, message: string, sessionId: string) => {
      try {
        // 获取会话历史
        const history = await this.aiService.getSessionHistory(sessionId);
        return this.aiService.sendMessage(message, sessionId, history);
      } catch (error) {
        console.error('AI消息处理失败:', error);
        throw error;
      }
    });
  }
}

// 创建应用实例
new Application();
