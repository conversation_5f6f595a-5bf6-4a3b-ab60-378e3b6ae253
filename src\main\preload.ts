import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { IPC_CHANNELS } from '@shared/constants';

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 窗口控制
  window: {
    minimize: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MINIMIZE),
    maximize: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_MAXIMIZE),
    close: () => ipcRenderer.invoke(IPC_CHANNELS.WINDOW_CLOSE),
  },

  // 应用控制
  app: {
    quit: () => ipcRenderer.invoke(IPC_CHANNELS.APP_QUIT),
    getVersion: () => ipcRenderer.invoke(IPC_CHANNELS.APP_VERSION),
  },

  // 数据存储
  store: {
    get: (key: string) => ipcRenderer.invoke(IPC_CHANNELS.STORE_GET, key),
    set: (key: string, value: any) => ipc<PERSON>enderer.invoke(IPC_CHANNELS.STORE_SET, key, value),
    delete: (key: string) => ipcRenderer.invoke(IPC_CHANNELS.STORE_DELETE, key),
  },

  // AI API
  ai: {
    sendMessage: (message: string, sessionId: string) => 
      ipcRenderer.invoke(IPC_CHANNELS.AI_SEND_MESSAGE, message, sessionId),
    
    // 监听流式响应
    onStreamChunk: (callback: (data: any) => void) => {
      ipcRenderer.on(IPC_CHANNELS.AI_STREAM_CHUNK, (_, data) => callback(data));
    },
    
    onStreamEnd: (callback: (data: any) => void) => {
      ipcRenderer.on(IPC_CHANNELS.AI_STREAM_END, (_, data) => callback(data));
    },
    
    onError: (callback: (error: string) => void) => {
      ipcRenderer.on(IPC_CHANNELS.AI_ERROR, (_, error) => callback(error));
    },

    // 移除监听器
    removeAllListeners: () => {
      ipcRenderer.removeAllListeners(IPC_CHANNELS.AI_STREAM_CHUNK);
      ipcRenderer.removeAllListeners(IPC_CHANNELS.AI_STREAM_END);
      ipcRenderer.removeAllListeners(IPC_CHANNELS.AI_ERROR);
    }
  }
});

// 类型声明
declare global {
  interface Window {
    electronAPI: {
      window: {
        minimize: () => Promise<void>;
        maximize: () => Promise<void>;
        close: () => Promise<void>;
      };
      app: {
        quit: () => Promise<void>;
        getVersion: () => Promise<string>;
      };
      store: {
        get: (key: string) => Promise<any>;
        set: (key: string, value: any) => Promise<void>;
        delete: (key: string) => Promise<void>;
      };
      ai: {
        sendMessage: (message: string, sessionId: string) => Promise<void>;
        onStreamChunk: (callback: (data: any) => void) => void;
        onStreamEnd: (callback: (data: any) => void) => void;
        onError: (callback: (error: string) => void) => void;
        removeAllListeners: () => void;
      };
    };
  }
}
